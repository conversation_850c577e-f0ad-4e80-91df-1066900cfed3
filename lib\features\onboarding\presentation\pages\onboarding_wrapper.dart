// ignore_for_file: prefer_const_constructors

import 'dart:developer';

import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/common/widgets/large_button.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:cal/core/local_models/daily_data_model/daily_user_info_service.dart';
import 'package:cal/features/authentication/presentation/pages/authentication_page.dart';
import 'package:cal/features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/achieve_goal_visualaization_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/activate_notifications_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/add_cals_again.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/birthdate_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/frequency_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/gender_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/goal_selection_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/height_weight_screen.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/process_result_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/processing_infos_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/target_weight_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/used_other_apps_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/weight_chart_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/weight_goal_visualization.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/weight_change_rate_screen.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/welcome_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/what_doesnot_make_you_commit_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/what_is_your_diet_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/what_you_want_to_achieve_content.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/where_did_hear_of_us.dart';
import 'package:cal/features/onboarding/presentation/pages/onboarding_content/with_orange_ai_content.dart';
import 'package:cal/features/onboarding/presentation/widgets/custom_linear_progress_indicator.dart';
import 'package:cal/features/onboarding/presentation/widgets/language_button.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:throttling/throttling.dart';
import 'package:toastification/toastification.dart';

import '../../../../common/utils/init_main.dart';

class OnboardingWrapper extends StatefulWidget {
  const OnboardingWrapper({super.key, this.isFromSettingsToChangeTarget = false, this.model});

  final bool isFromSettingsToChangeTarget;
  final DailyUserDataModel? model;

  @override
  State<OnboardingWrapper> createState() => _OnboardingWrapperState();
}

class _OnboardingWrapperState extends State<OnboardingWrapper> {
  final deb = Debouncing<void>(duration: const Duration(milliseconds: 200));

  // final AppleHealthConfig appleHealthConfig = AppleHealthConfig();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<OnboardingBloc>()..add(InitializeBloc(isFromSettings: widget.isFromSettingsToChangeTarget)),
      child: BlocBuilder<OnboardingBloc, OnboardingState>(
        builder: (context, state) {
          return Scaffold(
            body: Container(
              color: Colors.transparent,
              padding: EdgeInsets.only(left: context.screenWidth * .05, right: context.screenWidth * .05, top: 48, bottom: 32),
              child: Column(
                children: [
                  // Header with back button, progress indicator, and language button
                  _buildHeader(context, state),

                  // Dynamic PageView based on flow
                  Flexible(
                    child: PageView(
                      controller: context.read<OnboardingBloc>().pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: _buildScreensFromFlow(context, state),
                    ),
                  ),

                  // Next button
                  _buildNextButton(context, state),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context, OnboardingState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Back button (only show if not on first screen)
        if (state.flowIndex > 0)
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => deb.debounce(() {
              context.read<OnboardingBloc>().add(PreviousStep());
            }),
          )
        else
          const SizedBox(width: 48), // Placeholder for alignment

        // Progress indicator
        Expanded(
          child: CustomLinearProgressIndicator(
            value: state.progressPercentage,
          ),
        ),

        const SizedBox(width: 10),

        // Language button
        LangButton.buildLanguageButton(context),
      ],
    );
  }

  List<Widget> _buildScreensFromFlow(BuildContext context, OnboardingState state) {
    // Map of screen IDs to their widget implementations
    final screenWidgets = {
      'gender': GenderContent(),
      'frequency': FrequencyContent(),
      'where_did_hear_of_us': WhereDidHearOfUs(),
      'did_use_other_app_content': DidUseOtherAppContent(),
      'weight_chart_content': WeightChartContent(),
      'height_weight': HeightWeightContent(),
      'birth_date': BirthDateContent(),
      'goal_selection': GoalSelectionContent(isFromSettings: widget.isFromSettingsToChangeTarget),
      'target_weight': TargetWeightContent(),
      'weight_visualization': WeightGoalVisualizationContent(),
      'weight_change_rate': WeightChangeRateContent(),
      'with_orange_ai_content': WithOrangeAiContent(),
      'what_doesnot_make_you_commit': WhatDoesnotMakeYouCommitContent(),
      'what_is_your_diet': WhatIsYourDietContent(),
      'what_you_want_to_achieve': WhatYouWantToAchieveContent(),
      'achieve_goal_visualaization': AchieveGoalVisualaizationContent(),
      // 'meals_time_content': MealsTimeContent(),
      'activate_notifications': ActivateNotificationsContent(),
      // 'apple_health_content': AppleHealthContent(),
      'add_cals_again': AddCalsAgain(),
      // 'add_yesterday_cals_content': AddYesterdayCalsContent(),
      // 'referral_code_content': ReferralCodeContent(),
      'welcome': WelcomeContent(),
      'processing_info': ProcessingInfosContent(),
      'result': ProcessResultContent(),
      // 'free_trail_content': FreeTrailContent(),
    };

    // Return widgets based on flow screen IDs
    return state.flowScreenIds.map((screenId) {
      return screenWidgets[screenId] ?? Container();
    }).toList();
  }

  Widget _buildNextButton(BuildContext context, OnboardingState state) {
    // Handle cases where flowScreenIds might not be initialized yet
    if (state.flowScreenIds.isEmpty || state.flowIndex < 0 || state.flowIndex >= state.flowScreenIds.length) {
      return LargeButton(
        onPressed: () {
          // This case should ideally not be reached if InitializeBloc is working correctly
          // but as a fallback, we can log or show a generic next button
          log("Flow IDs not initialized or invalid index. Showing default Next button.");
          context.read<OnboardingBloc>().add(NextStep(context));
        },
        text: LocaleKeys.onboarding_next.tr(),
        circularRadius: 16,
        backgroundColor: context.primaryColor,
        textStyle: context.textTheme.bodyMedium!.copyWith(
          color: context.onPrimaryColor,
          fontWeight: FontWeight.w700,
        ),
      );
    }

    final currentScreenId = state.flowScreenIds[state.flowIndex];

    bool isYesNoButtons(String screenId) => ['add_yesterday_cals_content', 'add_cals_again'].contains(screenId);
    bool isSubmitScreen(String screenId) => ['processing_info'].contains(screenId);
    bool isSkipButton(String screenId) => ['apple_health_content'].contains(screenId);
    bool shouldDisableButton = isSubmitScreen(currentScreenId) && !state.isButtonActive;
    log("isSkipButton ${isSkipButton(currentScreenId)}");
    // Customize behavior for specific screen(s)
    if (isYesNoButtons(currentScreenId)) {
      return Row(
        children: [
          Expanded(
            child: LargeButton(
              onPressed: () {
                // Custom behavior for 'No' button
                context.read<OnboardingBloc>().add(UpdateAddCalsAgainToTarget(true));
                context.read<OnboardingBloc>().add(NextStep(context));
              },
              text: LocaleKeys.onboarding_yes.tr(),
              backgroundColor: context.primaryColor,
              circularRadius: 16,
              textStyle: context.textTheme.bodyMedium!.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          SizedBox(width: 10), // Add some spacing between buttons
          Expanded(
            child: LargeButton(
              onPressed: () {
                context.read<OnboardingBloc>().add(UpdateAddCalsAgainToTarget(false));
                context.read<OnboardingBloc>().add(NextStep(context));
              },
              text: LocaleKeys.onboarding_no.tr(),
              backgroundColor: context.primaryColor,
              circularRadius: 16,
              textStyle: context.textTheme.bodyMedium!.copyWith(
                color: context.onPrimaryColor,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      );
    } else if (isSkipButton(currentScreenId)) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LargeButton(
            onPressed: () {
              log("${state.flowIndex.toString()} page count");
              if (state.isCurrentScreenValid()) {
                context.read<OnboardingBloc>().add(NextStep(context));
              } else {
                toastification.show(
                  context: context,
                  type: ToastificationType.error,
                  style: ToastificationStyle.fillColored,
                  title: Text(LocaleKeys.onboarding_please_select_an_option.tr()),
                  autoCloseDuration: const Duration(seconds: 3),
                );
              }
            },
            text:
                state.flowIndex == state.flowScreenIds.length - 1 ? LocaleKeys.onboarding_lets_start.tr() : LocaleKeys.onboarding_next.tr(),
            circularRadius: 16,
            backgroundColor: context.primaryColor,
            textStyle: context.textTheme.bodyMedium!.copyWith(
              color: context.onPrimaryColor,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      );
    }

    // Default "Next"/"Let's start" button
    return LargeButton(
      onPressed: shouldDisableButton
          ? () {}
          : () async {
              log("${state.flowIndex.toString()} page count");
              if (state.isCurrentScreenValid()) {
                if (state.flowIndex == state.flowScreenIds.length - 1) {
                  if (widget.isFromSettingsToChangeTarget) {
                    await DailyUserInfoService.saveDailyData(widget.model!.copyWith(
                      targetCalories: state.cals!.toDouble(),
                      targetCarbs: state.carbs!.toDouble(),
                      targetProtein: state.protein!.toDouble(),
                      targetFat: state.fat!.toDouble(),
                    )).then((val) {
                      if (context.mounted) context.pop();
                    });
                  } else {
                    context.read<OnboardingBloc>().add(SubmitOnboarding());
                    ShPH.saveData(key: AppKeys.hasOnboarded, value: true);

                    // context.pushReplacement(SubscriptionPlansWrapper());
                    context.pushReplacement(AuthenticationPage());
                  }
                } else {
                  if (currentScreenId == 'activate_notifications' && ShPH.getData(key: AppKeys.fbToken) != "") {
                    // Initialize notifications but don't block the UI if it hangs
                    Initialization.initNotifications().then((val) {
                      if (context.mounted) context.read<OnboardingBloc>().add(NextStep(context));
                    }).catchError((error) {
                      if (context.mounted) context.read<OnboardingBloc>().add(NextStep(context));
                    });
                  } else {
                    context.read<OnboardingBloc>().add(NextStep(context));
                  }
                }
              } else {
                toastification.show(
                  context: context,
                  type: ToastificationType.error,
                  style: ToastificationStyle.fillColored,
                  title: Text(LocaleKeys.onboarding_please_select_an_option.tr()),
                  autoCloseDuration: const Duration(seconds: 3),
                );
              }
            },
      text: state.flowIndex == state.flowScreenIds.length - 1 ? LocaleKeys.onboarding_lets_start.tr() : LocaleKeys.onboarding_next.tr(),
      circularRadius: 16,
      backgroundColor: shouldDisableButton ? context.primaryColor.withAlpha(150) : context.primaryColor,
      textStyle: context.textTheme.bodyMedium!.copyWith(
        color: context.onPrimaryColor,
        fontWeight: FontWeight.w700,
      ),
    );
  }
}
